/**
 * 内容特征提取器
 * 从各种内容中提取特征用于推荐算法
 */
import { EventEmitter } from '../../utils/EventEmitter';
import * as THREE from 'three';

export interface ContentFeatureExtractorConfig {
  debug?: boolean;
  cacheEnabled?: boolean;
  extractionTimeout?: number;  // 提取超时时间(ms)
  maxCacheSize?: number;       // 最大缓存大小
  enableMLModels?: boolean;    // 启用机器学习模型
  enableImageProcessing?: boolean; // 启用图像处理
  enableAudioAnalysis?: boolean;   // 启用音频分析
  enableNLP?: boolean;         // 启用自然语言处理
  enablePerformanceMonitoring?: boolean; // 启用性能监控
  qualityThreshold?: number;   // 特征质量阈值
  vectorDimension?: number;    // 特征向量维度
}

/**
 * 机器学习模型配置
 */
export interface MLModelConfig {
  modelType: 'cnn' | 'rnn' | 'transformer' | 'autoencoder';
  inputShape: number[];
  outputDimension: number;
  weights?: ArrayBuffer;
}

/**
 * 特征质量评估结果
 */
export interface FeatureQuality {
  score: number;           // 质量分数 (0-1)
  completeness: number;    // 完整性 (0-1)
  consistency: number;     // 一致性 (0-1)
  reliability: number;     // 可靠性 (0-1)
  issues: string[];        // 质量问题列表
}

/**
 * 性能统计
 */
export interface PerformanceStats {
  totalExtractions: number;
  successfulExtractions: number;
  failedExtractions: number;
  averageExtractionTime: number;
  cacheHitRate: number;
  memoryUsage: number;
  lastUpdated: Date;
}

// 内容类型
export enum ContentType {
  ASSET = 'asset',
  SCENE = 'scene',
  MATERIAL = 'material',
  ANIMATION = 'animation',
  SCRIPT = 'script',
  TEXTURE = 'texture',
  MODEL = 'model',
  AUDIO = 'audio'
}

// 内容特征
export interface ContentFeature {
  id: string;
  type: ContentType;
  features: FeatureVector;
  metadata: ContentMetadata;
  extractedAt: Date;
  version: string;
}

// 特征向量
export interface FeatureVector {
  visual?: VisualFeatures;
  semantic?: SemanticFeatures;
  technical?: TechnicalFeatures;
  behavioral?: BehavioralFeatures;
  similarity?: SimilarityFeatures;
}

// 视觉特征
export interface VisualFeatures {
  colorPalette: ColorInfo[];
  dominantColors: string[];
  brightness: number;          // 亮度 (0-1)
  contrast: number;           // 对比度 (0-1)
  saturation: number;         // 饱和度 (0-1)
  complexity: number;         // 复杂度 (0-1)
  style: string[];            // 风格标签
  composition: CompositionInfo;
}

// 颜色信息
export interface ColorInfo {
  color: string;              // 十六进制颜色
  percentage: number;         // 占比 (0-1)
  position: string;           // 位置描述
}

// 构图信息
export interface CompositionInfo {
  symmetry: number;           // 对称性 (0-1)
  balance: number;            // 平衡性 (0-1)
  focusPoints: Point2D[];     // 焦点位置
  ruleOfThirds: number;       // 三分法符合度 (0-1)
}

export interface Point2D {
  x: number;
  y: number;
}

// 语义特征
export interface SemanticFeatures {
  tags: string[];             // 标签
  categories: string[];       // 分类
  keywords: string[];         // 关键词
  description: string;        // 描述
  sentiment: number;          // 情感倾向 (-1 到 1)
  topics: TopicInfo[];        // 主题信息
  entities: EntityInfo[];     // 实体信息
}

// 主题信息
export interface TopicInfo {
  topic: string;
  confidence: number;         // 置信度 (0-1)
  weight: number;             // 权重 (0-1)
}

// 实体信息
export interface EntityInfo {
  entity: string;
  type: string;               // 实体类型
  confidence: number;         // 置信度 (0-1)
}

// 技术特征
export interface TechnicalFeatures {
  fileSize: number;           // 文件大小(bytes)
  dimensions: Dimensions;     // 尺寸信息
  format: string;             // 文件格式
  quality: number;            // 质量评分 (0-1)
  performance: PerformanceInfo;
  compatibility: string[];    // 兼容性
  requirements: RequirementInfo;
}

// 尺寸信息
export interface Dimensions {
  width?: number;
  height?: number;
  depth?: number;
  duration?: number;          // 持续时间(秒)
  frameRate?: number;         // 帧率
}

// 性能信息
export interface PerformanceInfo {
  renderTime: number;         // 渲染时间(ms)
  memoryUsage: number;        // 内存使用(MB)
  polygonCount?: number;      // 多边形数量
  textureSize?: number;       // 纹理大小(MB)
}

// 需求信息
export interface RequirementInfo {
  minCPU: string;
  minMemory: number;          // MB
  minGPU?: string;
  minStorage: number;         // MB
}

// 行为特征
export interface BehavioralFeatures {
  popularity: number;         // 受欢迎程度 (0-1)
  usageFrequency: number;     // 使用频率
  userRating: number;         // 用户评分 (0-5)
  downloadCount: number;      // 下载次数
  viewCount: number;          // 查看次数
  shareCount: number;         // 分享次数
  lastUsed: Date;             // 最后使用时间
  trendingScore: number;      // 趋势评分 (0-1)
}

// 相似性特征
export interface SimilarityFeatures {
  similarItems: string[];     // 相似项目ID
  clusters: string[];         // 聚类标识
  embeddings: number[];       // 嵌入向量
  fingerprint: string;        // 内容指纹
}

// 内容元数据
export interface ContentMetadata {
  title: string;
  author: string;
  createdAt: Date;
  updatedAt: Date;
  version: string;
  license: string;
  source: string;
  language: string;
  region: string;
  accessibility: AccessibilityInfo;
}

// 可访问性信息
export interface AccessibilityInfo {
  hasAltText: boolean;
  hasSubtitles: boolean;
  colorBlindFriendly: boolean;
  screenReaderCompatible: boolean;
}

/**
 * 内容特征提取器
 */
export class ContentFeatureExtractor {
  private config: ContentFeatureExtractorConfig;
  private eventEmitter: EventEmitter = new EventEmitter();

  // 特征缓存
  private featureCache: Map<string, ContentFeature> = new Map();

  // 提取器映射
  private extractors: Map<ContentType, FeatureExtractor> = new Map();

  // 机器学习模型
  private mlModels: Map<string, MLModelConfig> = new Map();

  // 性能统计
  private performanceStats: PerformanceStats;

  // 特征向量化器
  private vectorizer: FeatureVectorizer;

  // 质量评估器
  private qualityAssessor: FeatureQualityAssessor;

  // 聚类器
  private clusterer: FeatureClusterer;

  constructor(config: ContentFeatureExtractorConfig = {}) {
    this.config = {
      debug: false,
      cacheEnabled: true,
      extractionTimeout: 30000,
      maxCacheSize: 1000,
      enableMLModels: false,
      enableImageProcessing: true,
      enableAudioAnalysis: true,
      enableNLP: true,
      enablePerformanceMonitoring: true,
      qualityThreshold: 0.6,
      vectorDimension: 512,
      ...config
    };

    this.initializeComponents();
    this.initializeExtractors();
    this.initializePerformanceStats();

    if (this.config.debug) {
      console.log('内容特征提取器初始化完成');
    }
  }

  /**
   * 初始化组件
   */
  private initializeComponents(): void {
    // 初始化向量化器
    this.vectorizer = new FeatureVectorizer(this.config.vectorDimension);

    // 初始化质量评估器
    this.qualityAssessor = new FeatureQualityAssessor();

    // 初始化聚类器
    this.clusterer = new FeatureClusterer();
  }

  /**
   * 初始化性能统计
   */
  private initializePerformanceStats(): void {
    this.performanceStats = {
      totalExtractions: 0,
      successfulExtractions: 0,
      failedExtractions: 0,
      averageExtractionTime: 0,
      cacheHitRate: 0,
      memoryUsage: 0,
      lastUpdated: new Date()
    };
  }

  /**
   * 初始化特征提取器
   */
  private initializeExtractors(): void {
    this.extractors.set(ContentType.ASSET, new AssetFeatureExtractor());
    this.extractors.set(ContentType.SCENE, new SceneFeatureExtractor());
    this.extractors.set(ContentType.MATERIAL, new MaterialFeatureExtractor());
    this.extractors.set(ContentType.ANIMATION, new AnimationFeatureExtractor());
    this.extractors.set(ContentType.SCRIPT, new ScriptFeatureExtractor());
    this.extractors.set(ContentType.TEXTURE, new TextureFeatureExtractor());
    this.extractors.set(ContentType.MODEL, new ModelFeatureExtractor());
    this.extractors.set(ContentType.AUDIO, new AudioFeatureExtractor());
  }

  /**
   * 提取内容特征
   */
  public async extractFeatures(
    contentId: string,
    contentType: ContentType,
    contentData: any
  ): Promise<ContentFeature> {
    const startTime = performance.now();
    const cacheKey = `${contentId}:${contentType}`;

    // 检查缓存
    if (this.config.cacheEnabled && this.featureCache.has(cacheKey)) {
      const cached = this.featureCache.get(cacheKey)!;
      if (this.isCacheValid(cached)) {
        return cached;
      }
    }

    try {
      // 获取对应的特征提取器
      const extractor = this.extractors.get(contentType);
      if (!extractor) {
        throw new Error(`不支持的内容类型: ${contentType}`);
      }

      // 设置提取超时
      const extractionPromise = extractor.extract(contentId, contentData);
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('特征提取超时')), this.config.extractionTimeout);
      });

      const features = await Promise.race([extractionPromise, timeoutPromise]);

      const contentFeature: ContentFeature = {
        id: contentId,
        type: contentType,
        features,
        metadata: await this.extractMetadata(contentId, contentData),
        extractedAt: new Date(),
        version: '1.0'
      };

      // 缓存结果
      if (this.config.cacheEnabled) {
        this.cacheFeature(cacheKey, contentFeature);
      }

      // 更新性能统计
      const extractionTime = performance.now() - startTime;
      if (this.config.enablePerformanceMonitoring) {
        this.updatePerformanceStats(extractionTime, true);
      }

      // 触发特征提取完成事件
      this.eventEmitter.emit('features.extracted', {
        contentId,
        contentType,
        featuresCount: this.countFeatures(features)
      });

      if (this.config.debug) {
        console.log(`特征提取完成: ${contentId}, 类型: ${contentType}`);
      }

      return contentFeature;

    } catch (error) {
      // 更新性能统计
      const extractionTime = performance.now() - startTime;
      if (this.config.enablePerformanceMonitoring) {
        this.updatePerformanceStats(extractionTime, false);
      }

      console.error(`特征提取失败: ${contentId}`, error);
      this.eventEmitter.emit('features.error', { contentId, contentType, error });
      throw error;
    }
  }

  /**
   * 批量提取特征
   */
  public async batchExtractFeatures(
    requests: Array<{
      contentId: string;
      contentType: ContentType;
      contentData: any;
    }>
  ): Promise<ContentFeature[]> {
    const results: ContentFeature[] = [];
    const batchSize = 5; // 并发处理数量

    for (let i = 0; i < requests.length; i += batchSize) {
      const batch = requests.slice(i, i + batchSize);
      const batchResults = await Promise.allSettled(
        batch.map(request => 
          this.extractFeatures(request.contentId, request.contentType, request.contentData)
        )
      );

      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          console.error(`批量特征提取失败: ${batch[index].contentId}`, result.reason);
        }
      });
    }

    return results;
  }

  /**
   * 计算内容相似性
   */
  public async calculateSimilarity(
    feature1: ContentFeature,
    feature2: ContentFeature
  ): Promise<number> {
    let similarity = 0;
    let weightSum = 0;

    // 视觉相似性
    if (feature1.features.visual && feature2.features.visual) {
      const visualSim = this.calculateVisualSimilarity(
        feature1.features.visual,
        feature2.features.visual
      );
      similarity += visualSim * 0.3;
      weightSum += 0.3;
    }

    // 语义相似性
    if (feature1.features.semantic && feature2.features.semantic) {
      const semanticSim = this.calculateSemanticSimilarity(
        feature1.features.semantic,
        feature2.features.semantic
      );
      similarity += semanticSim * 0.4;
      weightSum += 0.4;
    }

    // 技术相似性
    if (feature1.features.technical && feature2.features.technical) {
      const technicalSim = this.calculateTechnicalSimilarity(
        feature1.features.technical,
        feature2.features.technical
      );
      similarity += technicalSim * 0.2;
      weightSum += 0.2;
    }

    // 行为相似性
    if (feature1.features.behavioral && feature2.features.behavioral) {
      const behavioralSim = this.calculateBehavioralSimilarity(
        feature1.features.behavioral,
        feature2.features.behavioral
      );
      similarity += behavioralSim * 0.1;
      weightSum += 0.1;
    }

    return weightSum > 0 ? similarity / weightSum : 0;
  }

  /**
   * 查找相似内容
   */
  public async findSimilarContent(
    targetFeature: ContentFeature,
    candidateFeatures: ContentFeature[],
    threshold: number = 0.7,
    maxResults: number = 10
  ): Promise<Array<{ feature: ContentFeature; similarity: number }>> {
    const similarities: Array<{ feature: ContentFeature; similarity: number }> = [];

    for (const candidate of candidateFeatures) {
      if (candidate.id === targetFeature.id) {
        continue; // 跳过自己
      }

      const similarity = await this.calculateSimilarity(targetFeature, candidate);
      if (similarity >= threshold) {
        similarities.push({ feature: candidate, similarity });
      }
    }

    // 按相似度排序并返回前N个结果
    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, maxResults);
  }

  /**
   * 更新特征
   */
  public async updateFeatures(
    contentId: string,
    contentType: ContentType,
    contentData: any
  ): Promise<ContentFeature> {
    const cacheKey = `${contentId}:${contentType}`;
    
    // 清除缓存
    this.featureCache.delete(cacheKey);
    
    // 重新提取特征
    return await this.extractFeatures(contentId, contentType, contentData);
  }

  /**
   * 获取特征统计信息
   */
  public getFeatureStats(): {
    totalCached: number;
    cacheHitRate: number;
    extractionCount: number;
    errorCount: number;
  } {
    return {
      totalCached: this.featureCache.size,
      cacheHitRate: 0.85, // 示例值
      extractionCount: 0,  // 示例值
      errorCount: 0        // 示例值
    };
  }

  /**
   * 清理缓存
   */
  public clearCache(): void {
    this.featureCache.clear();
    if (this.config.debug) {
      console.log('特征缓存已清理');
    }
  }

  // 私有辅助方法
  private isCacheValid(feature: ContentFeature): boolean {
    const maxAge = 24 * 60 * 60 * 1000; // 24小时
    return Date.now() - feature.extractedAt.getTime() < maxAge;
  }

  private cacheFeature(key: string, feature: ContentFeature): void {
    // 检查缓存大小限制
    if (this.featureCache.size >= this.config.maxCacheSize!) {
      // 删除最旧的缓存项
      const oldestKey = this.featureCache.keys().next().value;
      this.featureCache.delete(oldestKey);
    }

    this.featureCache.set(key, feature);
  }

  private async extractMetadata(contentId: string, contentData: any): Promise<ContentMetadata> {
    // 实现元数据提取逻辑
    return {
      title: contentData.title || contentId,
      author: contentData.author || 'unknown',
      createdAt: contentData.createdAt || new Date(),
      updatedAt: contentData.updatedAt || new Date(),
      version: contentData.version || '1.0',
      license: contentData.license || 'unknown',
      source: contentData.source || 'local',
      language: contentData.language || 'en',
      region: contentData.region || 'global',
      accessibility: {
        hasAltText: false,
        hasSubtitles: false,
        colorBlindFriendly: false,
        screenReaderCompatible: false
      }
    };
  }

  private countFeatures(features: FeatureVector): number {
    let count = 0;
    if (features.visual) count++;
    if (features.semantic) count++;
    if (features.technical) count++;
    if (features.behavioral) count++;
    if (features.similarity) count++;
    return count;
  }

  private calculateVisualSimilarity(visual1: VisualFeatures, visual2: VisualFeatures): number {
    // 计算视觉特征相似性
    let similarity = 0;
    let count = 0;

    // 亮度相似性
    similarity += 1 - Math.abs(visual1.brightness - visual2.brightness);
    count++;

    // 对比度相似性
    similarity += 1 - Math.abs(visual1.contrast - visual2.contrast);
    count++;

    // 饱和度相似性
    similarity += 1 - Math.abs(visual1.saturation - visual2.saturation);
    count++;

    // 复杂度相似性
    similarity += 1 - Math.abs(visual1.complexity - visual2.complexity);
    count++;

    // 主色调相似性
    const colorSimilarity = this.calculateColorSimilarity(
      visual1.dominantColors,
      visual2.dominantColors
    );
    similarity += colorSimilarity;
    count++;

    return count > 0 ? similarity / count : 0;
  }

  private calculateSemanticSimilarity(semantic1: SemanticFeatures, semantic2: SemanticFeatures): number {
    // 计算语义特征相似性
    let similarity = 0;
    let count = 0;

    // 情感相似性
    similarity += 1 - Math.abs(semantic1.sentiment - semantic2.sentiment);
    count++;

    // 关键词相似性
    const keywordSimilarity = this.calculateKeywordSimilarity(
      semantic1.keywords,
      semantic2.keywords
    );
    similarity += keywordSimilarity;
    count++;

    // 主题相似性
    const topicSimilarity = this.calculateTopicSimilarity(
      semantic1.topics.map(t => t.topic),
      semantic2.topics.map(t => t.topic)
    );
    similarity += topicSimilarity;
    count++;

    // 实体相似性
    const entitySimilarity = this.calculateEntitySimilarity(
      semantic1.entities.map(e => e.entity),
      semantic2.entities.map(e => e.entity)
    );
    similarity += entitySimilarity;
    count++;

    return count > 0 ? similarity / count : 0;
  }

  private calculateTechnicalSimilarity(technical1: TechnicalFeatures, technical2: TechnicalFeatures): number {
    // 计算技术特征相似性
    let similarity = 0;
    let count = 0;

    // 文件大小相似性（对数尺度）
    const size1 = Math.log(technical1.fileSize + 1);
    const size2 = Math.log(technical2.fileSize + 1);
    const maxSize = Math.max(size1, size2);
    if (maxSize > 0) {
      similarity += 1 - Math.abs(size1 - size2) / maxSize;
      count++;
    }

    // 质量相似性
    similarity += 1 - Math.abs(technical1.quality - technical2.quality);
    count++;

    // 尺寸相似性
    if (technical1.dimensions.width && technical2.dimensions.width) {
      const width1 = Math.log(technical1.dimensions.width + 1);
      const width2 = Math.log(technical2.dimensions.width + 1);
      const maxWidth = Math.max(width1, width2);
      if (maxWidth > 0) {
        similarity += 1 - Math.abs(width1 - width2) / maxWidth;
        count++;
      }
    }

    if (technical1.dimensions.height && technical2.dimensions.height) {
      const height1 = Math.log(technical1.dimensions.height + 1);
      const height2 = Math.log(technical2.dimensions.height + 1);
      const maxHeight = Math.max(height1, height2);
      if (maxHeight > 0) {
        similarity += 1 - Math.abs(height1 - height2) / maxHeight;
        count++;
      }
    }

    // 格式相似性
    const formatSimilarity = technical1.format === technical2.format ? 1 : 0;
    similarity += formatSimilarity;
    count++;

    return count > 0 ? similarity / count : 0;
  }

  private calculateBehavioralSimilarity(behavioral1: BehavioralFeatures, behavioral2: BehavioralFeatures): number {
    // 计算行为特征相似性
    let similarity = 0;
    let count = 0;

    // 流行度相似性
    similarity += 1 - Math.abs(behavioral1.popularity - behavioral2.popularity);
    count++;

    // 用户评分相似性
    similarity += 1 - Math.abs(behavioral1.userRating - behavioral2.userRating) / 5;
    count++;

    // 下载量相似性（对数尺度）
    const download1 = Math.log(behavioral1.downloadCount + 1);
    const download2 = Math.log(behavioral2.downloadCount + 1);
    const maxDownload = Math.max(download1, download2);
    if (maxDownload > 0) {
      similarity += 1 - Math.abs(download1 - download2) / maxDownload;
      count++;
    }

    // 趋势相似性
    similarity += 1 - Math.abs(behavioral1.trendingScore - behavioral2.trendingScore);
    count++;

    return count > 0 ? similarity / count : 0;
  }

  /**
   * 计算颜色相似性
   */
  private calculateColorSimilarity(colors1: string[], colors2: string[]): number {
    if (colors1.length === 0 && colors2.length === 0) return 1;
    if (colors1.length === 0 || colors2.length === 0) return 0;

    let totalSimilarity = 0;
    let count = 0;

    for (const color1 of colors1) {
      let maxSimilarity = 0;
      for (const color2 of colors2) {
        const similarity = this.calculateSingleColorSimilarity(color1, color2);
        maxSimilarity = Math.max(maxSimilarity, similarity);
      }
      totalSimilarity += maxSimilarity;
      count++;
    }

    return count > 0 ? totalSimilarity / count : 0;
  }

  /**
   * 计算单个颜色相似性
   */
  private calculateSingleColorSimilarity(color1: string, color2: string): number {
    // 将颜色转换为RGB
    const rgb1 = this.hexToRgb(color1);
    const rgb2 = this.hexToRgb(color2);

    if (!rgb1 || !rgb2) return 0;

    // 计算欧几里得距离
    const distance = Math.sqrt(
      Math.pow(rgb1.r - rgb2.r, 2) +
      Math.pow(rgb1.g - rgb2.g, 2) +
      Math.pow(rgb1.b - rgb2.b, 2)
    );

    // 归一化到0-1范围
    return 1 - distance / (255 * Math.sqrt(3));
  }

  /**
   * 十六进制颜色转RGB
   */
  private hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  /**
   * 计算关键词相似性
   */
  private calculateKeywordSimilarity(keywords1: string[], keywords2: string[]): number {
    if (keywords1.length === 0 && keywords2.length === 0) return 1;
    if (keywords1.length === 0 || keywords2.length === 0) return 0;

    const set1 = new Set(keywords1.map(k => k.toLowerCase()));
    const set2 = new Set(keywords2.map(k => k.toLowerCase()));

    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    return intersection.size / union.size; // Jaccard相似性
  }

  /**
   * 计算主题相似性
   */
  private calculateTopicSimilarity(topics1: string[], topics2: string[]): number {
    if (topics1.length === 0 && topics2.length === 0) return 1;
    if (topics1.length === 0 || topics2.length === 0) return 0;

    const set1 = new Set(topics1.map(t => t.toLowerCase()));
    const set2 = new Set(topics2.map(t => t.toLowerCase()));

    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    return intersection.size / union.size;
  }

  /**
   * 计算实体相似性
   */
  private calculateEntitySimilarity(entities1: string[], entities2: string[]): number {
    if (entities1.length === 0 && entities2.length === 0) return 1;
    if (entities1.length === 0 || entities2.length === 0) return 0;

    const set1 = new Set(entities1.map(e => e.toLowerCase()));
    const set2 = new Set(entities2.map(e => e.toLowerCase()));

    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    return intersection.size / union.size;
  }

  /**
   * 获取性能统计
   */
  public getPerformanceStats(): PerformanceStats {
    return { ...this.performanceStats };
  }

  /**
   * 重置性能统计
   */
  public resetPerformanceStats(): void {
    this.initializePerformanceStats();
  }

  /**
   * 获取特征向量
   */
  public vectorizeFeatures(features: FeatureVector): number[] {
    return this.vectorizer.vectorize(features);
  }

  /**
   * 评估特征质量
   */
  public assessFeatureQuality(features: FeatureVector): FeatureQuality {
    return this.qualityAssessor.assessQuality(features);
  }

  /**
   * 聚类特征
   */
  public clusterFeatures(features: FeatureVector[], method: 'kmeans' | 'hierarchical' = 'kmeans'): string[] {
    const vectors = features.map(f => this.vectorizer.vectorize(f));

    if (method === 'kmeans') {
      const k = Math.min(Math.ceil(Math.sqrt(vectors.length)), 10);
      return this.clusterer.kMeansCluster(vectors, k);
    } else {
      return this.clusterer.hierarchicalCluster(vectors);
    }
  }

  /**
   * 更新性能统计
   */
  private updatePerformanceStats(extractionTime: number, success: boolean): void {
    this.performanceStats.totalExtractions++;

    if (success) {
      this.performanceStats.successfulExtractions++;
    } else {
      this.performanceStats.failedExtractions++;
    }

    // 更新平均提取时间
    const totalTime = this.performanceStats.averageExtractionTime * (this.performanceStats.totalExtractions - 1) + extractionTime;
    this.performanceStats.averageExtractionTime = totalTime / this.performanceStats.totalExtractions;

    // 更新缓存命中率
    const cacheHits = this.featureCache.size;
    this.performanceStats.cacheHitRate = cacheHits / this.performanceStats.totalExtractions;

    // 更新内存使用（估算）
    this.performanceStats.memoryUsage = this.featureCache.size * 1024; // 假设每个特征1KB

    this.performanceStats.lastUpdated = new Date();
  }

  /**
   * 事件监听
   */
  public addEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听
   */
  public removeEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }
}

// 特征提取器接口
export interface FeatureExtractor {
  extract(contentId: string, contentData: any): Promise<FeatureVector>;
}

// 各种特征提取器的基础实现
export class AssetFeatureExtractor implements FeatureExtractor {
  async extract(contentId: string, contentData: any): Promise<FeatureVector> {
    // 实现资产特征提取
    const features: FeatureVector = {};

    // 技术特征
    features.technical = {
      fileSize: contentData.size || 0,
      dimensions: {
        width: contentData.width,
        height: contentData.height,
        depth: contentData.depth
      },
      format: contentData.format || 'unknown',
      quality: this.calculateQuality(contentData),
      performance: {
        renderTime: contentData.renderTime || 0,
        memoryUsage: contentData.memoryUsage || 0,
        polygonCount: contentData.polygonCount,
        textureSize: contentData.textureSize
      },
      compatibility: contentData.compatibility || [],
      requirements: {
        minCPU: contentData.minCPU || 'unknown',
        minMemory: contentData.minMemory || 0,
        minGPU: contentData.minGPU,
        minStorage: contentData.minStorage || 0
      }
    };

    // 语义特征
    features.semantic = {
      tags: contentData.tags || [],
      categories: contentData.categories || [],
      keywords: this.extractKeywords(contentData),
      description: contentData.description || '',
      sentiment: this.analyzeSentiment(contentData.description || ''),
      topics: this.extractTopics(contentData),
      entities: this.extractEntities(contentData)
    };

    return features;
  }

  private calculateQuality(contentData: any): number {
    // 基于多个因素计算质量分数
    let quality = 0.5; // 基础分数

    if (contentData.resolution) {
      quality += Math.min(contentData.resolution / 2048, 0.3);
    }
    if (contentData.polygonCount) {
      quality += Math.min(contentData.polygonCount / 100000, 0.2);
    }

    return Math.min(quality, 1.0);
  }

  private extractKeywords(contentData: any): string[] {
    const text = (contentData.name || '') + ' ' + (contentData.description || '');
    return text.toLowerCase().split(/\s+/).filter(word => word.length > 2).slice(0, 10);
  }

  private analyzeSentiment(text: string): number {
    // 简化的情感分析
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'beautiful'];
    const negativeWords = ['bad', 'terrible', 'awful', 'ugly', 'poor'];

    let score = 0;
    const words = text.toLowerCase().split(/\s+/);

    for (const word of words) {
      if (positiveWords.includes(word)) score += 0.1;
      if (negativeWords.includes(word)) score -= 0.1;
    }

    return Math.max(-1, Math.min(1, score));
  }

  private extractTopics(contentData: any): TopicInfo[] {
    const topics: TopicInfo[] = [];

    if (contentData.categories) {
      for (const category of contentData.categories) {
        topics.push({
          topic: category,
          confidence: 0.8,
          weight: 0.5
        });
      }
    }

    return topics;
  }

  private extractEntities(contentData: any): EntityInfo[] {
    const entities: EntityInfo[] = [];

    if (contentData.author) {
      entities.push({
        entity: contentData.author,
        type: 'PERSON',
        confidence: 0.9
      });
    }

    return entities;
  }
}

export class SceneFeatureExtractor implements FeatureExtractor {
  async extract(contentId: string, contentData: any): Promise<FeatureVector> {
    // 实现场景特征提取
    const features: FeatureVector = {};

    // 视觉特征
    features.visual = {
      brightness: this.calculateBrightness(contentData),
      contrast: this.calculateContrast(contentData),
      saturation: this.calculateSaturation(contentData),
      complexity: this.calculateComplexity(contentData),
      dominantColors: this.extractDominantColors(contentData),
      colorDistribution: this.analyzeColorDistribution(contentData),
      composition: {
        symmetry: contentData.symmetry || 0.5,
        balance: contentData.balance || 0.5,
        focusPoints: contentData.focusPoints || [],
        ruleOfThirds: contentData.ruleOfThirds || 0.5
      }
    };

    // 技术特征
    features.technical = {
      fileSize: contentData.size || 0,
      dimensions: {
        width: contentData.width || 1920,
        height: contentData.height || 1080
      },
      format: contentData.format || 'scene',
      quality: this.calculateSceneQuality(contentData),
      performance: {
        renderTime: contentData.renderTime || 0,
        memoryUsage: contentData.memoryUsage || 0,
        polygonCount: contentData.totalPolygons || 0
      },
      compatibility: ['webgl', 'vulkan'],
      requirements: {
        minCPU: 'medium',
        minMemory: 512,
        minGPU: 'integrated',
        minStorage: contentData.size || 0
      }
    };

    return features;
  }

  private calculateBrightness(contentData: any): number {
    return contentData.lighting?.intensity || 0.5;
  }

  private calculateContrast(contentData: any): number {
    return contentData.lighting?.contrast || 0.5;
  }

  private calculateSaturation(contentData: any): number {
    return contentData.colorSaturation || 0.5;
  }

  private calculateComplexity(contentData: any): number {
    const objectCount = contentData.objects?.length || 0;
    return Math.min(objectCount / 100, 1.0);
  }

  private extractDominantColors(contentData: any): string[] {
    return contentData.dominantColors || ['#808080'];
  }

  private analyzeColorDistribution(contentData: any): ColorInfo[] {
    const colors = this.extractDominantColors(contentData);
    return colors.map((color, index) => ({
      color,
      percentage: 1 / colors.length,
      position: `area_${index}`
    }));
  }

  private calculateSceneQuality(contentData: any): number {
    let quality = 0.5;

    if (contentData.lighting) quality += 0.2;
    if (contentData.materials?.length > 0) quality += 0.2;
    if (contentData.objects?.length > 10) quality += 0.1;

    return Math.min(quality, 1.0);
  }
}

export class MaterialFeatureExtractor implements FeatureExtractor {
  async extract(contentId: string, contentData: any): Promise<FeatureVector> {
    const features: FeatureVector = {};

    // 视觉特征
    features.visual = {
      brightness: contentData.albedo?.brightness || 0.5,
      contrast: contentData.contrast || 0.5,
      saturation: contentData.saturation || 0.5,
      complexity: this.calculateMaterialComplexity(contentData),
      dominantColors: this.extractMaterialColors(contentData),
      colorPalette: this.extractMaterialColors(contentData).map(color => ({
        color,
        percentage: 1 / this.extractMaterialColors(contentData).length,
        position: 'material'
      })),
      style: ['material', contentData.type || 'standard'],
      composition: {
        symmetry: 0.5,
        balance: 0.5,
        focusPoints: [],
        ruleOfThirds: 0.5
      }
    };

    // 技术特征
    features.technical = {
      fileSize: contentData.size || 0,
      dimensions: {
        width: contentData.textureWidth || 512,
        height: contentData.textureHeight || 512
      },
      format: contentData.format || 'material',
      quality: this.calculateMaterialQuality(contentData),
      performance: {
        renderTime: contentData.shaderComplexity || 1,
        memoryUsage: contentData.textureMemory || 0
      },
      compatibility: ['pbr', 'standard'],
      requirements: {
        minCPU: 'low',
        minMemory: 128,
        minGPU: 'basic',
        minStorage: contentData.size || 0
      }
    };

    return features;
  }

  private calculateMaterialComplexity(contentData: any): number {
    let complexity = 0.3; // 基础复杂度

    if (contentData.normalMap) complexity += 0.2;
    if (contentData.roughnessMap) complexity += 0.2;
    if (contentData.metallicMap) complexity += 0.2;
    if (contentData.emissiveMap) complexity += 0.1;

    return Math.min(complexity, 1.0);
  }

  private extractMaterialColors(contentData: any): string[] {
    const colors = [];

    if (contentData.albedoColor) {
      colors.push(contentData.albedoColor);
    }
    if (contentData.emissiveColor) {
      colors.push(contentData.emissiveColor);
    }

    return colors.length > 0 ? colors : ['#808080'];
  }

  private calculateMaterialQuality(contentData: any): number {
    let quality = 0.5;

    if (contentData.textureWidth >= 1024) quality += 0.2;
    if (contentData.normalMap) quality += 0.1;
    if (contentData.pbrCompliant) quality += 0.2;

    return Math.min(quality, 1.0);
  }
}

export class AnimationFeatureExtractor implements FeatureExtractor {
  async extract(contentId: string, contentData: any): Promise<FeatureVector> {
    const features: FeatureVector = {};

    // 技术特征
    features.technical = {
      fileSize: contentData.size || 0,
      dimensions: {
        duration: contentData.duration || 0,
        frameRate: contentData.frameRate || 30
      },
      format: contentData.format || 'animation',
      quality: this.calculateAnimationQuality(contentData),
      performance: {
        renderTime: contentData.complexity || 1,
        memoryUsage: contentData.memoryUsage || 0
      },
      compatibility: ['fbx', 'gltf'],
      requirements: {
        minCPU: 'medium',
        minMemory: 256,
        minStorage: contentData.size || 0
      }
    };

    // 行为特征
    features.behavioral = {
      popularity: contentData.popularity || 0.5,
      usageFrequency: contentData.usageFrequency || 0,
      userRating: contentData.userRating || 3,
      downloadCount: contentData.downloadCount || 0,
      viewCount: contentData.viewCount || 0,
      shareCount: contentData.shareCount || 0,
      lastUsed: contentData.lastUsed || new Date(),
      trendingScore: contentData.trendingScore || 0.5
    };

    return features;
  }

  private calculateAnimationQuality(contentData: any): number {
    let quality = 0.5;

    if (contentData.frameRate >= 60) quality += 0.2;
    if (contentData.smoothness > 0.8) quality += 0.2;
    if (contentData.keyframeCount > 100) quality += 0.1;

    return Math.min(quality, 1.0);
  }
}

export class ScriptFeatureExtractor implements FeatureExtractor {
  async extract(contentId: string, contentData: any): Promise<FeatureVector> {
    const features: FeatureVector = {};

    // 语义特征
    features.semantic = {
      tags: contentData.tags || [],
      categories: ['script', contentData.language || 'javascript'],
      keywords: this.extractScriptKeywords(contentData),
      description: contentData.description || '',
      sentiment: 0, // 脚本通常是中性的
      topics: this.extractScriptTopics(contentData),
      entities: this.extractScriptEntities(contentData)
    };

    // 技术特征
    features.technical = {
      fileSize: contentData.size || 0,
      dimensions: {
        width: contentData.lineCount || 0
      },
      format: contentData.language || 'javascript',
      quality: this.calculateScriptQuality(contentData),
      performance: {
        renderTime: contentData.executionTime || 0,
        memoryUsage: contentData.memoryUsage || 0
      },
      compatibility: [contentData.language || 'javascript'],
      requirements: {
        minCPU: 'low',
        minMemory: 64,
        minStorage: contentData.size || 0
      }
    };

    return features;
  }

  private extractScriptKeywords(contentData: any): string[] {
    const code = contentData.code || '';
    const keywords = code.match(/\b(function|class|const|let|var|if|for|while)\b/g) || [];
    return [...new Set(keywords as string[])];
  }

  private extractScriptTopics(contentData: any): TopicInfo[] {
    const topics: TopicInfo[] = [];

    if (contentData.category) {
      topics.push({
        topic: contentData.category,
        confidence: 0.9,
        weight: 0.8
      });
    }

    return topics;
  }

  private extractScriptEntities(contentData: any): EntityInfo[] {
    const entities: EntityInfo[] = [];

    if (contentData.author) {
      entities.push({
        entity: contentData.author,
        type: 'PERSON',
        confidence: 0.9
      });
    }

    return entities;
  }

  private calculateScriptQuality(contentData: any): number {
    let quality = 0.5;

    if (contentData.hasComments) quality += 0.2;
    if (contentData.hasTests) quality += 0.2;
    if (contentData.complexity < 10) quality += 0.1;

    return Math.min(quality, 1.0);
  }
}

export class TextureFeatureExtractor implements FeatureExtractor {
  async extract(_contentId: string, contentData: any): Promise<FeatureVector> {
    const features: FeatureVector = {};

    // 视觉特征
    features.visual = {
      brightness: this.calculateTextureBrightness(contentData),
      contrast: this.calculateTextureContrast(contentData),
      saturation: this.calculateTextureSaturation(contentData),
      complexity: this.calculateTextureComplexity(contentData),
      dominantColors: this.extractTextureColors(contentData),
      colorPalette: this.analyzeColorPalette(contentData),
      style: ['texture', contentData.type || 'diffuse'],
      composition: {
        symmetry: contentData.symmetry || 0.5,
        balance: contentData.balance || 0.5,
        focusPoints: [],
        ruleOfThirds: 0.5
      }
    };

    // 技术特征
    features.technical = {
      fileSize: contentData.size || 0,
      dimensions: {
        width: contentData.width || 512,
        height: contentData.height || 512
      },
      format: contentData.format || 'png',
      quality: this.calculateTextureQuality(contentData),
      performance: {
        renderTime: 1,
        memoryUsage: this.calculateTextureMemory(contentData)
      },
      compatibility: ['webgl', 'vulkan', 'directx'],
      requirements: {
        minCPU: 'low',
        minMemory: 128,
        minGPU: 'basic',
        minStorage: contentData.size || 0
      }
    };

    return features;
  }

  private calculateTextureBrightness(contentData: any): number {
    return contentData.averageBrightness || 0.5;
  }

  private calculateTextureContrast(contentData: any): number {
    return contentData.contrast || 0.5;
  }

  private calculateTextureSaturation(contentData: any): number {
    return contentData.saturation || 0.5;
  }

  private calculateTextureComplexity(contentData: any): number {
    let complexity = 0.3;

    if (contentData.hasPattern) complexity += 0.3;
    if (contentData.hasNoise) complexity += 0.2;
    if (contentData.detailLevel > 0.5) complexity += 0.2;

    return Math.min(complexity, 1.0);
  }

  private extractTextureColors(contentData: any): string[] {
    return contentData.dominantColors || ['#808080'];
  }

  private analyzeColorPalette(contentData: any): ColorInfo[] {
    const colors = this.extractTextureColors(contentData);
    return colors.map((color, index) => ({
      color,
      percentage: 1 / colors.length,
      position: `region_${index}`
    }));
  }

  private calculateTextureQuality(contentData: any): number {
    let quality = 0.5;

    if (contentData.width >= 1024) quality += 0.2;
    if (contentData.format === 'png') quality += 0.1;
    if (contentData.hasAlpha) quality += 0.1;
    if (contentData.compression < 0.5) quality += 0.1;

    return Math.min(quality, 1.0);
  }

  private calculateTextureMemory(contentData: any): number {
    const width = contentData.width || 512;
    const height = contentData.height || 512;
    const channels = contentData.channels || 4;
    return (width * height * channels) / (1024 * 1024); // MB
  }
}

export class ModelFeatureExtractor implements FeatureExtractor {
  async extract(_contentId: string, contentData: any): Promise<FeatureVector> {
    const features: FeatureVector = {};

    // 技术特征
    features.technical = {
      fileSize: contentData.size || 0,
      dimensions: {
        width: contentData.boundingBox?.width,
        height: contentData.boundingBox?.height,
        depth: contentData.boundingBox?.depth
      },
      format: contentData.format || 'obj',
      quality: this.calculateModelQuality(contentData),
      performance: {
        renderTime: this.calculateRenderTime(contentData),
        memoryUsage: this.calculateModelMemory(contentData),
        polygonCount: contentData.polygonCount || 0,
        textureSize: contentData.textureSize || 0
      },
      compatibility: ['webgl', 'vulkan', 'opengl'],
      requirements: {
        minCPU: this.getMinCPU(contentData),
        minMemory: this.getMinMemory(contentData),
        minGPU: this.getMinGPU(contentData),
        minStorage: contentData.size || 0
      }
    };

    // 视觉特征（基于模型的几何特征）
    features.visual = {
      brightness: 0.5, // 模型本身没有亮度
      contrast: 0.5,
      saturation: 0.5,
      complexity: this.calculateGeometricComplexity(contentData),
      dominantColors: contentData.materialColors || ['#808080'],
      colorPalette: this.extractModelColorPalette(contentData),
      style: ['3d-model', contentData.category || 'generic'],
      composition: {
        symmetry: contentData.symmetry || 0.5,
        balance: contentData.balance || 0.5,
        focusPoints: [],
        ruleOfThirds: 0.5
      }
    };

    return features;
  }

  private calculateModelQuality(contentData: any): number {
    let quality = 0.5;

    if (contentData.polygonCount > 10000) quality += 0.2;
    if (contentData.hasUVMapping) quality += 0.1;
    if (contentData.hasNormals) quality += 0.1;
    if (contentData.hasTextures) quality += 0.1;

    return Math.min(quality, 1.0);
  }

  private calculateRenderTime(contentData: any): number {
    const polygons = contentData.polygonCount || 1000;
    return Math.log(polygons) / 10; // 简化的渲染时间估算
  }

  private calculateModelMemory(contentData: any): number {
    const polygons = contentData.polygonCount || 1000;
    const vertices = polygons * 3;
    return (vertices * 32) / (1024 * 1024); // MB，假设每个顶点32字节
  }

  private getMinCPU(contentData: any): string {
    const polygons = contentData.polygonCount || 1000;
    if (polygons > 100000) return 'high';
    if (polygons > 10000) return 'medium';
    return 'low';
  }

  private getMinMemory(contentData: any): number {
    const polygons = contentData.polygonCount || 1000;
    return Math.max(256, polygons / 1000 * 64);
  }

  private getMinGPU(contentData: any): string {
    const polygons = contentData.polygonCount || 1000;
    if (polygons > 50000) return 'dedicated';
    if (polygons > 10000) return 'integrated';
    return 'basic';
  }

  private calculateGeometricComplexity(contentData: any): number {
    const polygons = contentData.polygonCount || 1000;
    return Math.min(Math.log(polygons) / 15, 1.0);
  }

  private extractModelColorPalette(contentData: any): ColorInfo[] {
    const colors = contentData.materialColors || ['#808080'];
    return colors.map((color: string, index: number) => ({
      color,
      percentage: 1 / colors.length,
      position: `material_${index}`
    }));
  }
}

export class AudioFeatureExtractor implements FeatureExtractor {
  async extract(_contentId: string, contentData: any): Promise<FeatureVector> {
    const features: FeatureVector = {};

    // 技术特征
    features.technical = {
      fileSize: contentData.size || 0,
      dimensions: {
        duration: contentData.duration || 0,
        frameRate: contentData.sampleRate || 44100
      },
      format: contentData.format || 'mp3',
      quality: this.calculateAudioQuality(contentData),
      performance: {
        renderTime: contentData.duration || 0,
        memoryUsage: this.calculateAudioMemory(contentData)
      },
      compatibility: ['web-audio', 'html5'],
      requirements: {
        minCPU: 'low',
        minMemory: 64,
        minStorage: contentData.size || 0
      }
    };

    // 语义特征（基于音频内容）
    features.semantic = {
      tags: contentData.tags || [],
      categories: ['audio', contentData.genre || 'unknown'],
      keywords: this.extractAudioKeywords(contentData),
      description: contentData.description || '',
      sentiment: this.analyzeAudioSentiment(contentData),
      topics: this.extractAudioTopics(contentData),
      entities: this.extractAudioEntities(contentData)
    };

    return features;
  }

  private calculateAudioQuality(contentData: any): number {
    let quality = 0.5;

    if (contentData.sampleRate >= 44100) quality += 0.2;
    if (contentData.bitRate >= 128) quality += 0.2;
    if (contentData.channels === 2) quality += 0.1; // 立体声

    return Math.min(quality, 1.0);
  }

  private calculateAudioMemory(contentData: any): number {
    const duration = contentData.duration || 0;
    const sampleRate = contentData.sampleRate || 44100;
    const channels = contentData.channels || 2;
    return (duration * sampleRate * channels * 2) / (1024 * 1024); // MB
  }

  private extractAudioKeywords(contentData: any): string[] {
    const keywords = [];

    if (contentData.genre) keywords.push(contentData.genre);
    if (contentData.mood) keywords.push(contentData.mood);
    if (contentData.tempo) keywords.push(contentData.tempo);

    return keywords;
  }

  private analyzeAudioSentiment(contentData: any): number {
    // 基于音频特征的简化情感分析
    if (contentData.mood === 'happy') return 0.8;
    if (contentData.mood === 'sad') return -0.8;
    if (contentData.mood === 'energetic') return 0.6;
    if (contentData.mood === 'calm') return 0.2;

    return 0; // 中性
  }

  private extractAudioTopics(contentData: any): TopicInfo[] {
    const topics: TopicInfo[] = [];

    if (contentData.genre) {
      topics.push({
        topic: contentData.genre,
        confidence: 0.9,
        weight: 0.8
      });
    }

    return topics;
  }

  private extractAudioEntities(contentData: any): EntityInfo[] {
    const entities: EntityInfo[] = [];

    if (contentData.artist) {
      entities.push({
        entity: contentData.artist,
        type: 'PERSON',
        confidence: 0.9
      });
    }

    return entities;
  }
}

/**
 * 特征向量化器
 */
export class FeatureVectorizer {
  private dimension: number;

  constructor(dimension: number = 512) {
    this.dimension = dimension;
  }

  /**
   * 将特征向量化
   */
  public vectorize(features: FeatureVector): number[] {
    const vector = new Array(this.dimension).fill(0);
    let index = 0;

    // 视觉特征向量化
    if (features.visual && index < this.dimension) {
      vector[index++] = features.visual.brightness;
      vector[index++] = features.visual.contrast;
      vector[index++] = features.visual.saturation;
      vector[index++] = features.visual.complexity;

      // 颜色特征
      for (const color of features.visual.dominantColors.slice(0, 5)) {
        if (index < this.dimension) {
          vector[index++] = this.colorToNumber(color);
        }
      }
    }

    // 语义特征向量化
    if (features.semantic && index < this.dimension) {
      vector[index++] = features.semantic.sentiment;

      // 关键词哈希
      for (const keyword of features.semantic.keywords.slice(0, 10)) {
        if (index < this.dimension) {
          vector[index++] = this.hashString(keyword) / 1000000;
        }
      }
    }

    // 技术特征向量化
    if (features.technical && index < this.dimension) {
      vector[index++] = Math.log(features.technical.fileSize + 1) / 20;
      vector[index++] = features.technical.quality;

      if (features.technical.dimensions.width) {
        vector[index++] = Math.log(features.technical.dimensions.width + 1) / 15;
      }
      if (features.technical.dimensions.height) {
        vector[index++] = Math.log(features.technical.dimensions.height + 1) / 15;
      }
    }

    // 行为特征向量化
    if (features.behavioral && index < this.dimension) {
      vector[index++] = features.behavioral.popularity;
      vector[index++] = features.behavioral.userRating / 5;
      vector[index++] = Math.log(features.behavioral.downloadCount + 1) / 20;
      vector[index++] = features.behavioral.trendingScore;
    }

    return vector;
  }

  /**
   * 降维处理
   */
  public reduceDimensions(vectors: number[][], targetDim: number): number[][] {
    // 简化的PCA实现
    if (vectors.length === 0 || targetDim >= vectors[0].length) {
      return vectors;
    }

    // 计算均值
    const mean = this.calculateMean(vectors);

    // 中心化数据
    const centeredData = vectors.map(vector =>
      vector.map((val, i) => val - mean[i])
    );

    // 简化的主成分选择（取前targetDim个维度）
    return centeredData.map(vector => vector.slice(0, targetDim));
  }

  private colorToNumber(color: string): number {
    // 将颜色字符串转换为数值
    const hex = color.replace('#', '');
    return parseInt(hex, 16) / 16777215; // 归一化到0-1
  }

  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  private calculateMean(vectors: number[][]): number[] {
    if (vectors.length === 0) return [];

    const mean = new Array(vectors[0].length).fill(0);
    for (const vector of vectors) {
      for (let i = 0; i < vector.length; i++) {
        mean[i] += vector[i];
      }
    }

    return mean.map(val => val / vectors.length);
  }
}

/**
 * 特征质量评估器
 */
export class FeatureQualityAssessor {
  /**
   * 评估特征质量
   */
  public assessQuality(features: FeatureVector): FeatureQuality {
    let completeness = 0;
    let consistency = 0;
    let reliability = 0;
    const issues: string[] = [];

    // 评估完整性
    const featureCount = this.countNonEmptyFeatures(features);
    completeness = featureCount / 5; // 假设有5种特征类型

    // 评估一致性
    consistency = this.assessConsistency(features);

    // 评估可靠性
    reliability = this.assessReliability(features);

    // 检查质量问题
    if (completeness < 0.5) {
      issues.push('特征完整性不足');
    }
    if (consistency < 0.6) {
      issues.push('特征一致性较差');
    }
    if (reliability < 0.7) {
      issues.push('特征可靠性不高');
    }

    const score = (completeness + consistency + reliability) / 3;

    return {
      score,
      completeness,
      consistency,
      reliability,
      issues
    };
  }

  private countNonEmptyFeatures(features: FeatureVector): number {
    let count = 0;
    if (features.visual) count++;
    if (features.semantic) count++;
    if (features.technical) count++;
    if (features.behavioral) count++;
    if (features.similarity) count++;
    return count;
  }

  private assessConsistency(features: FeatureVector): number {
    // 简化的一致性评估
    let score = 1.0;

    // 检查视觉特征一致性
    if (features.visual) {
      if (features.visual.brightness < 0 || features.visual.brightness > 1) {
        score -= 0.2;
      }
      if (features.visual.contrast < 0 || features.visual.contrast > 1) {
        score -= 0.2;
      }
    }

    // 检查技术特征一致性
    if (features.technical) {
      if (features.technical.quality < 0 || features.technical.quality > 1) {
        score -= 0.2;
      }
    }

    return Math.max(0, score);
  }

  private assessReliability(features: FeatureVector): number {
    // 简化的可靠性评估
    let score = 1.0;

    // 基于特征数量评估
    const featureCount = this.countNonEmptyFeatures(features);
    if (featureCount < 3) {
      score -= 0.3;
    }

    // 基于数据完整性评估
    if (features.visual && features.visual.dominantColors.length === 0) {
      score -= 0.1;
    }
    if (features.semantic && features.semantic.keywords.length === 0) {
      score -= 0.1;
    }

    return Math.max(0, score);
  }
}

/**
 * 特征聚类器
 */
export class FeatureClusterer {
  private clusters: Map<string, number[][]> = new Map();

  /**
   * K-means聚类
   */
  public kMeansCluster(vectors: number[][], k: number, maxIterations: number = 100): string[] {
    if (vectors.length === 0 || k <= 0) {
      return [];
    }

    // 初始化聚类中心
    const centroids = this.initializeCentroids(vectors, k);
    const assignments = new Array(vectors.length).fill(0);

    for (let iteration = 0; iteration < maxIterations; iteration++) {
      let changed = false;

      // 分配点到最近的聚类中心
      for (let i = 0; i < vectors.length; i++) {
        const newAssignment = this.findNearestCentroid(vectors[i], centroids);
        if (newAssignment !== assignments[i]) {
          assignments[i] = newAssignment;
          changed = true;
        }
      }

      if (!changed) break;

      // 更新聚类中心
      this.updateCentroids(vectors, assignments, centroids, k);
    }

    return assignments.map(assignment => `cluster_${assignment}`);
  }

  /**
   * 层次聚类
   */
  public hierarchicalCluster(vectors: number[][], threshold: number = 0.5): string[] {
    const clusters: number[][] = vectors.map((_, index) => [index]);
    const assignments = vectors.map((_, index) => index);

    while (clusters.length > 1) {
      let minDistance = Infinity;
      let mergeIndices = [-1, -1];

      // 找到最近的两个聚类
      for (let i = 0; i < clusters.length; i++) {
        for (let j = i + 1; j < clusters.length; j++) {
          const distance = this.calculateClusterDistance(
            clusters[i].map(idx => vectors[idx]),
            clusters[j].map(idx => vectors[idx])
          );

          if (distance < minDistance) {
            minDistance = distance;
            mergeIndices = [i, j];
          }
        }
      }

      if (minDistance > threshold) break;

      // 合并聚类
      const [i, j] = mergeIndices;
      clusters[i] = clusters[i].concat(clusters[j]);
      clusters.splice(j, 1);

      // 更新分配
      for (const idx of clusters[i]) {
        assignments[idx] = i;
      }
    }

    return assignments.map(assignment => `cluster_${assignment}`);
  }

  private initializeCentroids(vectors: number[][], k: number): number[][] {
    const centroids: number[][] = [];
    const dimension = vectors[0].length;

    for (let i = 0; i < k; i++) {
      const centroid = new Array(dimension);
      for (let j = 0; j < dimension; j++) {
        centroid[j] = Math.random();
      }
      centroids.push(centroid);
    }

    return centroids;
  }

  private findNearestCentroid(vector: number[], centroids: number[][]): number {
    let minDistance = Infinity;
    let nearestIndex = 0;

    for (let i = 0; i < centroids.length; i++) {
      const distance = this.euclideanDistance(vector, centroids[i]);
      if (distance < minDistance) {
        minDistance = distance;
        nearestIndex = i;
      }
    }

    return nearestIndex;
  }

  private updateCentroids(vectors: number[][], assignments: number[], centroids: number[][], k: number): void {
    for (let i = 0; i < k; i++) {
      const clusterVectors = vectors.filter((_, index) => assignments[index] === i);

      if (clusterVectors.length > 0) {
        for (let j = 0; j < centroids[i].length; j++) {
          centroids[i][j] = clusterVectors.reduce((sum, vector) => sum + vector[j], 0) / clusterVectors.length;
        }
      }
    }
  }

  private euclideanDistance(vector1: number[], vector2: number[]): number {
    let sum = 0;
    for (let i = 0; i < vector1.length; i++) {
      sum += Math.pow(vector1[i] - vector2[i], 2);
    }
    return Math.sqrt(sum);
  }

  private calculateClusterDistance(cluster1: number[][], cluster2: number[][]): number {
    // 使用平均链接距离
    let totalDistance = 0;
    let count = 0;

    for (const vector1 of cluster1) {
      for (const vector2 of cluster2) {
        totalDistance += this.euclideanDistance(vector1, vector2);
        count++;
      }
    }

    return count > 0 ? totalDistance / count : Infinity;
  }
}
